#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import re
import logging
import sys
import os
from datetime import datetime, timedelta, timezone
import time
from typing import Dict, List, Optional, Tuple
from utils.database import db
from utils.base_plugin import BasePlugin, log_execution
import json

class MimaCrawlerService(BasePlugin):
    def __init__(self):
        super().__init__("地图密码爬虫")
        self.china_tz = timezone(timedelta(hours=8))
        self.last_dev_update = None
        
    def initialize(self):
        """初始化服务"""
        try:
            # 设置日志
            log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
            os.makedirs(log_dir, exist_ok=True)
            
            # 配置根日志记录器
            root_logger = logging.getLogger()
            root_logger.setLevel(logging.INFO)
            
            # 清除现有的处理器
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)
            
            # 创建文件处理器
            log_file = os.path.join(log_dir, 'mima_crawler.log')
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(logging.INFO)
            
            # 创建控制台处理器
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.INFO)
            
            # 创建格式化器
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            # 添加处理器到根日志记录器
            root_logger.addHandler(file_handler)
            root_logger.addHandler(console_handler)
            
            # 输出初始化信息
            logging.info("="*50)
            logging.info("地图密码爬虫服务启动中...")
            logging.info(f"日志文件路径: {log_file}")
            logging.info(f"当前工作目录: {os.getcwd()}")
            
            # 输出当前运行模式
            mode = "开发" if self.is_debug_mode() else "生产"
            logging.info(f"当前运行模式: {mode}")
            
            # 确保日志立即写入
            for handler in root_logger.handlers:
                handler.flush()
                
        except Exception as e:
            print(f"初始化日志系统失败: {str(e)}")
            raise

    def notify(self, message: str, level: str = 'info') -> None:
        """发送通知到日志
        
        Args:
            message: 通知消息
            level: 日志级别 (info/warning/error)
        """
        log_func = getattr(logging, level.lower(), logging.info)
        log_func(message)

    def _parse_mima_data(self, desc: str) -> Dict[str, str]:
        """解析密码数据字符串

        Args:
            desc: 包含密码数据的字符串，格式为"零号大坝:4376;\n长弓溪谷:1118;\n巴克什:0948;\n航天基地:7116"

        Returns:
            Dict[str, str]: 密码数据字典，保持原始字符串格式以保留前导零
        """
        result = {
            'daba': None,
            'changgong': None,
            'bakeshi': None,
            'hangtian': None,
            'chaoxijianyu': None
        }

        try:
            # 使用正则表达式提取数字字符串，保持原始格式
            daba_match = re.search(r'零号大坝[：:]\s*(\d+)', desc)
            changgong_match = re.search(r'长弓溪谷[：:]\s*(\d+)', desc)
            bakeshi_match = re.search(r'巴克什[：:]\s*(\d+)', desc)
            hangtian_match = re.search(r'航天基地[：:]\s*(\d+)', desc)
            chaoxijianyu_match = re.search(r'潮汐监狱[：:]\s*(\d+)', desc)

            if daba_match:
                result['daba'] = daba_match.group(1)  # 保持字符串格式
            if changgong_match:
                result['changgong'] = changgong_match.group(1)  # 保持字符串格式
            if bakeshi_match:
                result['bakeshi'] = bakeshi_match.group(1)  # 保持字符串格式
            if hangtian_match:
                result['hangtian'] = hangtian_match.group(1)  # 保持字符串格式
            if chaoxijianyu_match:
                result['chaoxijianyu'] = chaoxijianyu_match.group(1)  # 保持字符串格式

            # 记录解析结果
            self.debug_info(f"解析密码数据: {result}")

        except Exception as e:
            self.notify(f"解析密码数据失败: {str(e)}", 'error')

        return result

    def _get_empty_result(self) -> Dict:
        """返回空的结果结构"""
        return {
            'title': '',
            'update_time': datetime.now(self.china_tz).strftime("%Y-%m-%d %H:%M:%S"),
            'passwords': [],
            'images': [],
            'total_maps': 0,
            'raw_data': {
                'daba': None,
                'changgong': None,
                'bakeshi': None,
                'hangtian': None,
                'chaoxijianyu': None
            }
        }

    def _parse_new_mima_data(self, title: str, content_text: str) -> Dict:
        """解析新API返回的密码数据

        Args:
            title: 文章标题
            content_text: 文章HTML内容

        Returns:
            Dict: 包含完整密码信息的字典
        """
        try:
            # 提取图片链接
            img_pattern = r'<img[^>]+src="([^"]+)"[^>]*>'
            images = re.findall(img_pattern, content_text)
            processed_images = []
            for img_url in images:
                if img_url.startswith('//'):
                    img_url = 'https:' + img_url
                processed_images.append(img_url)

            # 清理HTML标签
            clean_text = re.sub(r'<[^>]+>', '', content_text)
            clean_text = clean_text.replace('&nbsp;', ' ').replace('&amp;', '&')

            # 提取密码信息
            pattern = r'【([^】]+)】密码：(\d+)位置：([^【]*?)(?=【|$|\*)'
            matches = re.findall(pattern, clean_text)

            passwords = []
            raw_data = {
                'daba': None,
                'changgong': None,
                'bakeshi': None,
                'hangtian': None,
                'chaoxijianyu': None
            }

            for i, (map_name, password, location) in enumerate(matches):
                location = re.sub(r'\s+', ' ', location.strip())

                # 匹配对应的图片
                image_url = processed_images[i] if i < len(processed_images) else None

                password_info = {
                    'map': map_name,
                    'password': password,
                    'location': location,
                    'image': image_url
                }
                passwords.append(password_info)

                # 同时填充原始数据格式以保持兼容性
                if '零号大坝' in map_name or '大坝' in map_name:
                    raw_data['daba'] = password
                elif '长弓溪谷' in map_name or '长弓' in map_name:
                    raw_data['changgong'] = password
                elif '巴克什' in map_name:
                    raw_data['bakeshi'] = password
                elif '航天基地' in map_name or '航天' in map_name:
                    raw_data['hangtian'] = password
                elif '潮汐监狱' in map_name or '潮汐' in map_name:
                    raw_data['chaoxijianyu'] = password

            result = {
                'title': title,
                'update_time': datetime.now(self.china_tz).strftime("%Y-%m-%d %H:%M:%S"),
                'passwords': passwords,
                'images': processed_images,
                'total_maps': len(passwords),
                'raw_data': raw_data
            }

            self.debug_info(f"解析完成，共找到 {len(passwords)} 个地图密码")
            return result

        except Exception as e:
            self.notify(f"解析密码数据失败: {str(e)}", 'error')
            return self._get_empty_result()

    def fetch_mima_data(self) -> Dict:
        """从新的无需登录API获取最新的地图密码数据

        Returns:
            Dict: 包含完整密码信息的字典，包括标题、密码、位置、图片等
        """
        # 使用新的无需登录API
        base_url = "https://comm.ams.game.qq.com/ide/"

        headers = {
            "Host": "comm.ams.game.qq.com",
            "Connection": "keep-alive",
            "xweb_xhr": "1",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781 NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF XWEB/14181",
            "Content-Type": "application/x-www-form-urlencoded;",
            "Accept": "*/*",
            "Sec-Fetch-Site": "cross-site",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://servicewechat.com/wx1c36464bbea2507a/79/page-frame.html",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9"
        }
        
        try:
            # 第一步：获取资讯列表
            self.debug_info("开始获取资讯列表...")
            news_data = """iChartId=352143&iSubChartId=352143&sIdeToken=YWRywA&eas_url=http%3A%2F%2Fwechatmini.qq.com%2Fdfm%2Fwx1c36464bbea2507a%2Fpages%2Findex%2Findex%2F&method=gicp.tags.query&source=2&param=%7B%22typeIDs%22%3A%5B%221%22%2C%222%22%5D%2C%22tagIDs%22%3A%5B%22135654%2C135655%2C135656%22%5D%2C%22sortBy%22%3A%22sIdxTime%22%2C%22page%22%3A1%2C%22limit%22%3A10%2C%22queryLike%22%3Atrue%7D"""

            response = requests.post(base_url, headers=headers, data=news_data)

            if response.status_code != 200:
                self.notify(f"获取资讯列表失败，状态码: {response.status_code}", 'error')
                return self._get_empty_result()

            news_response = response.json()
            self.debug_info(f"资讯列表API响应: {news_response.get('iRet')} - {news_response.get('sMsg')}")

            if news_response.get('iRet') != 0:
                self.notify(f"资讯列表API返回错误: {news_response.get('sMsg')}", 'error')
                return self._get_empty_result()

            # 查找密码文章
            password_article = None
            jdata = news_response.get('jData', {})
            if jdata.get('data', {}).get('code') == 0:
                news_list = jdata.get('data', {}).get('data', {}).get('list', {})

                # 显示完整的资讯列表信息
                logging.info("="*60)
                logging.info("📰 API资讯列表完整信息:")
                logging.info("="*60)

                total_articles = 0
                for category_key, article_list in news_list.items():
                    logging.info(f"📂 分类 [{category_key}]:")

                    # 检查 article_list 是否为列表
                    if not isinstance(article_list, list):
                        logging.warning(f"   ⚠️  分类 [{category_key}] 的数据不是列表格式: {type(article_list)}")
                        continue

                    for i, article in enumerate(article_list, 1):
                        # 检查 article 是否为字典
                        if not isinstance(article, dict):
                            logging.warning(f"   ⚠️  文章 {i} 的数据不是字典格式: {type(article)} - {article}")
                            continue

                        total_articles += 1
                        title = article.get('title', '')
                        thread_id = article.get('threadID', '')
                        # 使用正确的时间字段
                        created_at = article.get('createdAt', '')
                        # API中似乎没有更新时间字段，使用创建时间
                        update_time = created_at
                        author_info = article.get('author', {})
                        author = author_info.get('name', '') if isinstance(author_info, dict) else str(author_info)
                        view_count = article.get('viewCount', 0)
                        reply_count = article.get('replyCount', 0)
                        collect_count = article.get('collectCount', 0)
                        liked_count = article.get('likedCount', 0)

                        # 显示原始时间数据用于调试
                        logging.info(f"      🔍 原始时间数据 - createdAt: {repr(created_at)}")

                        # 显示文章的所有字段用于调试（仅第一篇文章）
                        if i == 1:
                            logging.info(f"      📋 文章所有字段: {list(article.keys())}")
                            # 显示完整的文章数据结构
                            logging.info(f"      📄 完整文章数据: {article}")

                        # 转换时间戳为可读格式
                        if created_at:
                            try:
                                # 尝试不同的时间格式
                                if isinstance(created_at, (int, float)):
                                    # 时间戳格式
                                    create_dt = datetime.fromtimestamp(int(created_at), self.china_tz)
                                elif isinstance(created_at, str) and created_at.isdigit():
                                    # 字符串时间戳
                                    create_dt = datetime.fromtimestamp(int(created_at), self.china_tz)
                                else:
                                    # 其他格式，尝试解析
                                    create_dt = datetime.fromisoformat(str(created_at).replace('Z', '+00:00'))
                                    create_dt = create_dt.astimezone(self.china_tz)
                                create_time_str = create_dt.strftime('%Y-%m-%d %H:%M:%S')
                            except Exception as e:
                                create_time_str = f"解析失败: {created_at} (错误: {e})"
                        else:
                            create_time_str = "未知"

                        # 更新时间使用创建时间
                        update_time_str = create_time_str

                        logging.info(f"  {i:2d}. 📄 标题: {title}")
                        logging.info(f"      🆔 ID: {thread_id}")
                        logging.info(f"      👤 作者: {author}")
                        logging.info(f"      📅 创建时间: {create_time_str}")
                        logging.info(f"      🔄 更新时间: {update_time_str}")
                        logging.info(f"      👀 浏览数: {view_count} | 💬 回复数: {reply_count} | ❤️ 点赞数: {liked_count} | ⭐ 收藏数: {collect_count}")

                        # 显示文章摘要（如果有）
                        summary = article.get('summary', '')
                        if summary:
                            logging.info(f"      📝 摘要: {summary[:100]}{'...' if len(summary) > 100 else ''}")

                        # 显示封面图片（如果有）
                        cover = article.get('cover', '')
                        if cover:
                            logging.info(f"      🖼️ 封面: {cover}")

                        # 检查是否为密码文章
                        is_password_article = '密码' in title and ('彩蛋门' in title or '彩蛋' in title)
                        if is_password_article:
                            logging.info(f"      ⭐ 【匹配】这是密码文章!")
                            password_article = article

                        logging.info("")

                logging.info(f"📊 总计: {total_articles} 篇文章")
                logging.info("="*60)

                # 如果找到了密码文章，额外显示详细信息
                if password_article:
                    logging.info(f"🎯 已选择密码文章: {password_article.get('title', '')}")
                    logging.info(f"🆔 文章ID: {password_article.get('threadID', '')}")
                else:
                    logging.info("❌ 未找到匹配的密码文章")
                logging.info("="*60)

            if not password_article:
                self.notify("未找到密码相关文章", 'warning')
                return self._get_empty_result()

            # 第二步：获取文章详情
            thread_id = password_article.get('threadID')
            self.debug_info(f"正在获取文章详情 (ID: {thread_id})...")

            detail_data = f"""iChartId=352143&iSubChartId=352143&sIdeToken=YWRywA&eas_url=http%3A%2F%2Fwechatmini.qq.com%2Fdfm%2Fwx1c36464bbea2507a%2Fpages%2Findex%2Findex%2F&method=thread.detail&source=2&param=%7B%22threadID%22%3A{thread_id}%7D"""

            detail_response = requests.post(base_url, headers=headers, data=detail_data)

            if detail_response.status_code != 200:
                self.notify(f"获取文章详情失败，状态码: {detail_response.status_code}", 'error')
                return self._get_empty_result()

            detail_json = detail_response.json()

            if detail_json.get('iRet') != 0:
                self.notify(f"文章详情API返回错误: {detail_json.get('sMsg')}", 'error')
                return self._get_empty_result()

            # 解析文章内容
            content_data = detail_json.get('jData', {}).get('data', {}).get('data', {})
            title = content_data.get('title', '')
            content_text = content_data.get('content', {}).get('text', '')

            self.debug_info(f"成功获取文章: {title}")

            # 提取并处理数据
            return self._parse_new_mima_data(title, content_text)

        except requests.RequestException as e:
            self.notify(f"请求密码数据API失败: {str(e)}", 'error')
        except json.JSONDecodeError as e:
            self.notify(f"解析API响应JSON失败: {str(e)}", 'error')
        except Exception as e:
            self.notify(f"获取密码数据失败: {str(e)}", 'error')

        return self._get_empty_result()
    
    def _save_mima_data(self, mima_data: Dict) -> bool:
        """保存密码数据到数据库

        Args:
            mima_data: 包含完整密码信息的字典

        Returns:
            bool: 是否成功保存数据
        """
        try:
            with db as conn:
                # 准备JSON数据和原始数据
                json_data = json.dumps(mima_data, ensure_ascii=False)
                raw_data = mima_data.get('raw_data', {})

                # 查询现有记录，获取实际ID（而不是假设为1）
                existing_record = conn.execute_single("SELECT id FROM ba_sjz_ditumima ORDER BY id DESC LIMIT 1")

                if existing_record is None:
                    # 没有找到记录，或查询失败
                    self.debug_info("未找到现有记录，将插入新记录")

                    # 尝试插入包含json_data的记录，如果字段不存在则回退到原始格式
                    try:
                        query = """
                            INSERT INTO ba_sjz_ditumima
                                (daba, changgong, bakeshi, hangtian, chaoxijianyu, json_data)
                            VALUES
                                (%s, %s, %s, %s, %s, %s)
                        """
                        affected_rows = conn.execute_update(
                            query,
                            (
                                raw_data.get('daba'),
                                raw_data.get('changgong'),
                                raw_data.get('bakeshi'),
                                raw_data.get('hangtian'),
                                raw_data.get('chaoxijianyu'),
                                json_data
                            )
                        )
                    except Exception as e:
                        if "Unknown column 'json_data'" in str(e):
                            self.debug_info("json_data字段不存在，使用原始格式插入")
                            query = """
                                INSERT INTO ba_sjz_ditumima
                                    (daba, changgong, bakeshi, hangtian, chaoxijianyu)
                                VALUES
                                    (%s, %s, %s, %s, %s)
                            """
                            affected_rows = conn.execute_update(
                                query,
                                (
                                    raw_data.get('daba'),
                                    raw_data.get('changgong'),
                                    raw_data.get('bakeshi'),
                                    raw_data.get('hangtian'),
                                    raw_data.get('chaoxijianyu')
                                )
                            )
                        else:
                            raise e
                    if affected_rows > 0:
                        self.debug_info("成功插入密码数据")
                    else:
                        self.notify("插入密码数据失败，未影响任何行", 'warning')
                        return False
                else:
                    # 找到记录，使用实际ID进行更新
                    record_id = existing_record['id']
                    self.debug_info(f"找到现有记录，ID: {record_id}，将进行更新")

                    # 尝试更新包含json_data的记录，如果字段不存在则回退到原始格式
                    try:
                        query = """
                            UPDATE ba_sjz_ditumima SET
                                daba = %s,
                                changgong = %s,
                                bakeshi = %s,
                                hangtian = %s,
                                chaoxijianyu = %s,
                                json_data = %s
                            WHERE id = %s
                        """
                        affected_rows = conn.execute_update(
                            query,
                            (
                                raw_data.get('daba'),
                                raw_data.get('changgong'),
                                raw_data.get('bakeshi'),
                                raw_data.get('hangtian'),
                                raw_data.get('chaoxijianyu'),
                                json_data,
                                record_id
                            )
                        )
                    except Exception as e:
                        if "Unknown column 'json_data'" in str(e):
                            self.debug_info("json_data字段不存在，使用原始格式更新")
                            query = """
                                UPDATE ba_sjz_ditumima SET
                                    daba = %s,
                                    changgong = %s,
                                    bakeshi = %s,
                                    hangtian = %s,
                                    chaoxijianyu = %s
                                WHERE id = %s
                            """
                            affected_rows = conn.execute_update(
                                query,
                                (
                                    raw_data.get('daba'),
                                    raw_data.get('changgong'),
                                    raw_data.get('bakeshi'),
                                    raw_data.get('hangtian'),
                                    raw_data.get('chaoxijianyu'),
                                    record_id
                                )
                            )
                        else:
                            raise e
                    if affected_rows > 0:
                        self.debug_info(f"成功更新密码数据，记录ID: {record_id}")
                    else:
                        self.notify(f"更新密码数据失败，记录ID: {record_id}，未影响任何行", 'warning')
                        return False
                
                # 获取并打印最新数据
                current = conn.execute_single("SELECT * FROM ba_sjz_ditumima ORDER BY id DESC LIMIT 1")
                if current:
                    self.debug_info(f"当前数据库中的密码数据: {current}")
                else:
                    self.notify("无法获取最新保存的密码数据", 'warning')
                    return False
                
                return True
                
        except Exception as e:
            self.notify(f"数据库操作失败: {str(e)}", 'error')
            return False
    
    def _should_update(self, current_time: datetime) -> bool:
        """判断是否应该更新数据
        
        Args:
            current_time: 当前时间
            
        Returns:
            bool: 是否应该更新
        """
        # 开发模式下，每次启动服务就更新一次，之后每10分钟更新一次
        if self.is_debug_mode():
            if self.last_dev_update is None or (current_time - self.last_dev_update).total_seconds() > 60:
                self.last_dev_update = current_time
                return True
            return False
        
        # 记录上次执行日期的属性（如果不存在则初始化）
        if not hasattr(self, '_last_update_date'):
            self._last_update_date = None
        
        # 生产模式下，每天00:00:29执行一次
        target_hour = 00
        target_minute = 00
        target_second_start = 29
        target_second_end = 30  # 设置1秒的窗口，避免错过执行点
        
        current_date = current_time.date()
        current_hour = current_time.hour
        current_minute = current_time.minute
        current_second = current_time.second
        
        # 检查是否在目标时间窗口内
        is_target_time = (
            current_hour == target_hour and
            current_minute == target_minute and
            target_second_start <= current_second <= target_second_end
        )
        
        # 检查今天是否已经执行过
        already_updated_today = (
            self._last_update_date is not None and
            self._last_update_date == current_date
        )
        
        # 如果在目标时间窗口内且今天还未执行过，则执行更新
        if is_target_time and not already_updated_today:
            self._last_update_date = current_date
            self.debug_info(f"触发定时更新：当前时间 {current_time:%H:%M:%S}，目标时间 {target_hour:02d}:{target_minute:02d}:{target_second_start:02d}")
            return True
        
        return False
    
    @log_execution
    def update_mima(self) -> None:
        """更新地图密码数据"""
        self.notify("开始获取并更新地图密码数据...")
        
        # 获取密码数据
        mima_data = self.fetch_mima_data()
        
        # 检查是否成功获取了数据
        if not mima_data or mima_data.get('total_maps', 0) == 0:
            self.notify("未能获取到有效的密码数据", 'warning')
            return

        # 保存数据到数据库
        if self._save_mima_data(mima_data):
            # 构建通知消息
            msg = f"地图密码更新成功！\n标题: {mima_data.get('title', '每日彩蛋门密码')}\n"
            msg += f"更新时间: {mima_data.get('update_time', '')}\n"
            msg += f"共获取 {mima_data.get('total_maps', 0)} 个地图密码:\n"

            # 显示密码信息
            for password_info in mima_data.get('passwords', []):
                msg += f"🗺️ {password_info.get('map', '')}: {password_info.get('password', '')}\n"
                if password_info.get('location'):
                    msg += f"   📍 位置: {password_info.get('location', '')}\n"

            self.notify(msg, 'info')
        else:
            self.notify("保存密码数据失败", 'error')

    @log_execution
    def run(self) -> None:
        """运行服务"""
        self.notify("服务启动")
        
        try:
            # 初始化上次更新日期
            self._last_update_date = None
            
            # 服务启动时立即执行一次更新
            self.notify("执行首次更新...")
            self.update_mima()
            
            # 记录服务启动时间
            service_start_time = datetime.now(self.china_tz)
            self.notify(f"服务启动时间: {service_start_time:%Y-%m-%d %H:%M:%S}")
            
            # 计算并显示下一次定时更新时间
            next_update_time = self._calculate_next_update_time(service_start_time)
            self.notify(f"下一次定时更新时间: {next_update_time:%Y-%m-%d %H:%M:%S}")
            
            while True:
                current_time = datetime.now(self.china_tz)
                
                # 根据运行模式判断是否需要更新
                if self._should_update(current_time):
                    self.notify(f"触发定时更新，当前时间: {current_time:%Y-%m-%d %H:%M:%S}")
                    try:
                        self.update_mima()
                        self.notify("定时更新执行完成")
                        
                        # 更新下一次定时更新时间
                        next_update_time = self._calculate_next_update_time(current_time)
                        self.notify(f"下一次定时更新时间: {next_update_time:%Y-%m-%d %H:%M:%S}")
                    except Exception as e:
                        self.notify(f"定时更新执行失败: {str(e)}", 'error')
                        # 尝试重置状态，以便下一分钟可以重试
                        self._last_update_date = None
                
                # 每分钟输出心跳信息
                elif current_time.second == 0:
                    mode = "开发" if self.is_debug_mode() else "生产"
                    next_update = ""
                    
                    if self.is_debug_mode() and self.last_dev_update:
                        next_time = self.last_dev_update + timedelta(minutes=1)
                        next_update = f"，下次更新时间: {next_time:%H:%M:%S}"
                    elif not self.is_debug_mode():
                        # 在生产模式下，显示下一次定时更新时间
                        if not hasattr(self, '_next_update_time') or self._next_update_time is None:
                            self._next_update_time = self._calculate_next_update_time(current_time)
                        next_update = f"，下次定时更新时间: {self._next_update_time:%Y-%m-%d %H:%M:%S}"
                        
                    self.debug_info(f"服务运行中... [{mode}模式] {current_time:%Y-%m-%d %H:%M:%S}{next_update}")
                
                time.sleep(1)
                
        except KeyboardInterrupt:
            self.notify("服务停止", 'warning')
        except Exception as e:
            self.notify(f"服务异常: {str(e)}", 'error')
            raise
            
    def _calculate_next_update_time(self, current_time: datetime) -> datetime:
        """计算下一次更新时间
        
        Args:
            current_time: 当前时间
            
        Returns:
            datetime: 下一次更新时间
        """
        # 设置目标更新时间为当天的00:00:29
        target_time = current_time.replace(
            hour=00,
            minute=30,
            second=29,
            microsecond=0
        )
        
        # 如果当前时间已经过了今天的目标时间，则设置为明天的目标时间
        if current_time >= target_time:
            target_time += timedelta(days=1)
            
        self._next_update_time = target_time
        return target_time

def test_fetch_without_login():
    """
    使用无需登录的 ide 接口直接请求，验证是否能返回 JSON。
    将打印 HTTP 状态码、iRet、sMsg 以及部分 jData 内容。
    """
    url = "https://comm.ams.game.qq.com/ide/"
    # 精简必要请求头，尽量不依赖小程序上下文
    headers_list = [
        {
            "Content-Type": "application/x-www-form-urlencoded",
            "Accept": "*/*",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome Test",
            "Origin": "https://comm.ams.game.qq.com",
            "Referer": "https://comm.ams.game.qq.com/",
        },
        # 若被风控，可尝试更贴近微信小程序 UA 与 Referer
        {
            "Host": "comm.ams.game.qq.com",
            "Connection": "keep-alive",
            "xweb_xhr": "1",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781 NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF XWEB/14181",
            "Content-Type": "application/x-www-form-urlencoded",
            "Accept": "*/*",
            "Sec-Fetch-Site": "cross-site",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://servicewechat.com/wx1c36464bbea2507a/79/page-frame.html",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept-Language": "zh-CN,zh;q=0.9"
        }
    ]

    # 你给定的参数组合（注意 param 已 URL 编码在整体 data 字符串里）
    data = (
        "iChartId=352143"
        "&iSubChartId=352143"
        "&sIdeToken=YWRywA"
        "&eas_url=http://wechatmini.qq.com/-/-/pages/index/index/"
        "&method=dfm/center.day.secret"
        "&source=2"
        "&param=%7B%22distType%22:%22bannerManage%22,%22contentType%22:%22xcxrwst,xcxrmzx,xcxmgj%22%7D"
    )

    last_exc = None
    for idx, headers in enumerate(headers_list, 1):
        try:
            print(f"[test] 尝试无登录请求 ide 接口（headers 方案 {idx}）...")
            resp = requests.post(url, headers=headers, data=data, timeout=15)
            print(f"[test] HTTP status: {resp.status_code}")
            # 尝试解析 JSON
            try:
                js = resp.json()
                print("[test] JSON 解析成功")
                i_ret = js.get("iRet")
                s_msg = js.get("sMsg")
                print(f"[test] iRet: {i_ret}, sMsg: {s_msg}")
                j_data = js.get("jData")
                # 打印 jData 的关键结构，但避免输出过长
                if isinstance(j_data, dict):
                    print(f"[test] jData keys: {list(j_data.keys())[:20]}")
                else:
                    print(f"[test] jData type: {type(j_data)}")
                # 全量打印结果供你核对（如过长可注释掉）
                print(json.dumps(js, ensure_ascii=False)[:5000])
                return
            except json.JSONDecodeError as je:
                print(f"[test] JSON 解析失败: {je}")
                print(f"[test] 响应文本片段: {resp.text[:1000]}")
        except Exception as e:
            last_exc = e
            print(f"[test] 请求异常: {e}")

    if last_exc:
        print(f"[test] 所有 headers 方案均失败，最后异常: {last_exc}")
    else:
        print("[test] 所有 headers 方案均请求完成但未拿到有效 JSON")
if __name__ == "__main__":
    try:
        # 设置基本的控制台日志，以防初始化前出现错误
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler(sys.stdout)]
        )
        
        logging.info("正在启动地图密码爬虫服务...")
        
        # 检查Python版本
        python_version = sys.version
        logging.info(f"Python版本: {python_version}")
        
        # 检查工作目录
        logging.info(f"当前工作目录: {os.getcwd()}")
        
        # 检查必要的目录
        scripts_dir = os.path.dirname(os.path.abspath(__file__))
        logging.info(f"脚本目录: {scripts_dir}")
        
        # 创建并运行服务
        service = MimaCrawlerService()
        service.initialize()  # 显式调用初始化
        service.run()
    except KeyboardInterrupt:
        logging.warning("收到键盘中断信号，服务正在停止...")
    except Exception as e:
        logging.error(f"服务启动失败: {str(e)}", exc_info=True)
        sys.exit(1)
    finally:
        logging.info("服务已停止") 