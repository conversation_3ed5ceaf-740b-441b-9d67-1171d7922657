#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QQ游戏API数据获取脚本
用于获取腾讯游戏中心的横幅管理数据
"""

import requests
import json
import time
from datetime import datetime
import logging
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/qq_game_api.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class QQGameAPIClient:
    """QQ游戏API客户端"""
    
    def __init__(self):
        self.base_url = "https://comm.ams.game.qq.com/ide/"
        self.session = requests.Session()
        self.setup_headers()
    
    def setup_headers(self):
        """设置请求头"""
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'xweb_xhr': '1',
            'Sec-Fetch-Site': 'cross-site',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://wechatmini.qq.com/',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'Host': 'comm.ams.game.qq.com',
            'Connection': 'keep-alive'
        })
    
    def get_banner_data(self, chart_id=352143, sub_chart_id=352143, ide_token="YWRywA"):
        """
        获取横幅管理数据
        
        Args:
            chart_id (int): 图表ID
            sub_chart_id (int): 子图表ID
            ide_token (str): IDE令牌
            
        Returns:
            dict: API响应数据
        """
        try:
            # 构建请求参数
            params = {
                'iChartId': chart_id,
                'iSubChartId': sub_chart_id,
                'sIdeToken': ide_token,
                'eas_url': 'http://wechatmini.qq.com/-/-/pages/index/index/',
                'method': 'dfm/center.day.secret',
                'source': '2',
                'param': json.dumps({
                    "distType": "bannerManage",
                    "contentType": "xcxrwst,xcxrmzx,xcxmgj"
                })
            }
            
            logger.info(f"正在请求数据，参数: {params}")
            
            # 发送POST请求
            response = self.session.post(
                self.base_url,
                params=params,
                data="",
                timeout=30
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            # 解析JSON响应
            data = response.json()
            logger.info(f"请求成功，响应状态码: {response.status_code}")
            
            return {
                'success': True,
                'data': data,
                'timestamp': datetime.now().isoformat(),
                'status_code': response.status_code
            }
            
        except requests.exceptions.RequestException as e:
            logger.error(f"请求失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return {
                'success': False,
                'error': f"JSON解析失败: {e}",
                'raw_response': response.text,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"未知错误: {e}")
            return {
                'success': False,
                'error': f"未知错误: {e}",
                'timestamp': datetime.now().isoformat()
            }
    
    def save_data(self, data, filename=None):
        """
        保存数据到文件
        
        Args:
            data (dict): 要保存的数据
            filename (str): 文件名，如果为None则自动生成
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"data/qq_game_banner_{timestamp}.json"
        
        # 确保目录存在
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"数据已保存到: {filename}")
            return filename
        except Exception as e:
            logger.error(f"保存文件失败: {e}")
            return None
    
    def run_crawler(self, save_to_file=True):
        """
        运行爬虫获取数据
        
        Args:
            save_to_file (bool): 是否保存到文件
            
        Returns:
            dict: 获取的数据
        """
        logger.info("开始获取QQ游戏横幅数据...")
        
        # 获取数据
        result = self.get_banner_data()
        
        if result['success']:
            logger.info("数据获取成功!")
            
            # 打印部分数据预览
            if 'data' in result:
                logger.info(f"数据预览: {json.dumps(result['data'], ensure_ascii=False, indent=2)[:500]}...")
            
            # 保存到文件
            if save_to_file:
                self.save_data(result)
        else:
            logger.error(f"数据获取失败: {result.get('error', '未知错误')}")
        
        return result

def main():
    """主函数"""
    # 创建客户端
    client = QQGameAPIClient()
    
    # 运行爬虫
    result = client.run_crawler()
    
    # 打印结果
    print("\n" + "="*50)
    print("QQ游戏API数据获取结果:")
    print("="*50)
    print(json.dumps(result, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    main()
