#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的QQ游戏API请求脚本
基于用户提供的代码结构
"""

import requests
import json

def get_qq_game_data():
    """获取QQ游戏数据"""
    
    url = "https://comm.ams.game.qq.com/ide/?iChartId=352143&iSubChartId=352143&sIdeToken=YWRywA&eas_url=http://wechatmini.qq.com/-/-/pages/index/index/&method=dfm/center.day.secret&source=2&param=%7B%22distType%22:%22bannerManage%22,%22contentType%22:%22xcxrwst,xcxrmzx,xcxmgj%22%7D"

    payload = ""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'xweb_xhr': '1',
        'Sec-Fetch-Site': 'cross-site',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Dest': 'empty',
        'Referer': 'https://wechatmini.qq.com/',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Content-Type': 'application/json',
        'Accept': '*/*',
        'Host': 'comm.ams.game.qq.com',
        'Connection': 'keep-alive'
    }

    try:
        print("正在发送请求...")
        response = requests.request("POST", url, headers=headers, data=payload)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print("\n响应内容:")
        print("="*50)
        
        # 尝试解析JSON
        try:
            json_data = response.json()
            print(json.dumps(json_data, ensure_ascii=False, indent=2))
        except json.JSONDecodeError:
            print("响应不是有效的JSON格式，原始内容:")
            print(response.text)
            
        return response
        
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None

def save_response_to_file(response, filename="qq_game_response.json"):
    """保存响应到文件"""
    if response is None:
        print("没有响应数据可保存")
        return
    
    try:
        # 尝试保存为JSON
        json_data = response.json()
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
        print(f"响应已保存到: {filename}")
    except json.JSONDecodeError:
        # 如果不是JSON，保存为文本
        txt_filename = filename.replace('.json', '.txt')
        with open(txt_filename, 'w', encoding='utf-8') as f:
            f.write(response.text)
        print(f"响应已保存到: {txt_filename}")
    except Exception as e:
        print(f"保存文件失败: {e}")

if __name__ == "__main__":
    print("QQ游戏API数据获取脚本")
    print("="*30)
    
    # 获取数据
    response = get_qq_game_data()
    
    # 保存到文件
    if response:
        save_response_to_file(response)
    
    print("\n脚本执行完成!")
